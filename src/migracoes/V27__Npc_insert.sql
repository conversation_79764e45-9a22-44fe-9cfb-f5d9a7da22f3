INSERT INTO NPC (nome, tipo, sala_atual, dialogo_padrao) VALUES
('<PERSON><PERSON><PERSON>', 'colega', 2, 'Os servidores precisam estar sempre funcionando...'),
('<PERSON>', 'almoxarife', 5, 'Preciso organizar esse estoque...'),
('<PERSON>', 'colega', 8, '<PERSON><PERSON><PERSON> estagiário! Como posso ajudar?'),
('<PERSON><PERSON><PERSON><PERSON>o', 'colega', 3, 'Os servidores precisam estar sempre funcionando...'),
('<PERSON>', 'almoxarife', 6, 'Preciso organizar esse estoque...'),
('<PERSON><PERSON><PERSON>', 'colega', 20, '<PERSON><PERSON><PERSON> estagiário! Como posso ajudar?'),
('<PERSON><PERSON><PERSON>', 'colega', 24, 'Os servidores precisam estar sempre funcionando...'),
('<PERSON><PERSON><PERSON>', 'almoxarife', 26, 'Preciso organizar esse estoque...'),
('<PERSON>', 'colega', 14, '<PERSON><PERSON><PERSON> estagiário! Como posso ajudar?'),
('<PERSON><PERSON>', 'recepcionista', 9, '<PERSON><PERSON><PERSON> estagi<PERSON>, outros funcionários relataram um problema para você resolver.');
('Carlos', 'supervisor', 4, 'Olá, sou o supervisor de TI. Tenho missões técnicas para você!'),
('Fernanda', 'supervisor', 12, 'Sou a supervisora administrativa. Tenho missões de organização e relatórios para você!');


INSERT INTO Dialogo(id_npc, dialogo) VALUES
(1, 'Os servidores precisam estar sempre funcionando. Já verificou o status deles hoje?'),
(2, 'Preciso organizar esse estoque... Se encontrar algum item perdido, me avise!'),
(3, 'Olá estagiário! Como posso ajudar?'),
(4, 'Os servidores precisam estar sempre funcionando. Já verificou o status deles hoje?'),
(5, 'Preciso organizar esse estoque... Se encontrar algum item perdido, me avise!'),
(6, 'Olá estagiário! Como posso ajudar?'),
(7, 'Os servidores precisam estar sempre funcionando. Já verificou o status deles hoje?'),
(8, 'Preciso organizar esse estoque... Se encontrar algum item perdido, me avise!'),
(9, 'Olá estagiário! Como posso ajudar?'),
(10, 'Olá estagiário, outros funcionários relataram um problema para você resolver. Os seguintes PCs estão com problemas: ');



