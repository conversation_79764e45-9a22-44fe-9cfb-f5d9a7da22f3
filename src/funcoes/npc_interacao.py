from database import call_db_function, get_connection

from database import get_connection

def get_supervisor_npcs(sala_id):
    conn = get_connection()
    with conn.cursor() as cur:
        cur.execute("""
            SELECT id_npc, nome FROM NPC
            WHERE tipo = 'supervisor' AND sala_atual = %s
        """, (sala_id,))
        supervisores = cur.fetchall()
    conn.close()
    return supervisores

def get_dialogo_supervisor(id_npc):
    conn = get_connection()
    with conn.cursor() as cur:
        cur.execute("SELECT dialogo FROM Dialogo WHERE id_npc = %s LIMIT 1;", (id_npc,))
        row = cur.fetchone()
    conn.close()
    return row[0] if row else "O supervisor não tem nada a dizer."

def get_missoes_disponiveis(id_supervisor, id_estagiario):
    conn = get_connection()
    with conn.cursor() as cur:
        cur.execute("""
            SELECT m.id_missao, m.nome, m.descricao, m.xp_recompensa
            FROM Missao m
            WHERE m.id_supervisor = %s
            AND m.id_missao NOT IN (
                SELECT id_missao FROM MissaoStatus WHERE id_estagiario = %s
            )
        """, (id_supervisor, id_estagiario))
        missoes = cur.fetchall()
    conn.close()
    return missoes

def aceitar_missao(id_estagiario, id_missao):
    conn = get_connection()
    with conn.cursor() as cur:
        cur.execute("""
            INSERT INTO MissaoStatus (id_missao, id_estagiario, status)
            VALUES (%s, %s, 'Disponível')
            ON CONFLICT (id_missao, id_estagiario) DO NOTHING
        """, (id_missao, id_estagiario))
        conn.commit()
    conn.close()

def get_missoes_status(id_estagiario):
    conn = get_connection()
    with conn.cursor() as cur:
        cur.execute("""
            SELECT m.nome, m.descricao, ms.status
            FROM MissaoStatus ms
            JOIN Missao m ON ms.id_missao = m.id_missao
            WHERE ms.id_estagiario = %s
        """, (id_estagiario,))
        missoes = cur.fetchall()
    conn.close()
    return missoes

def falar_com_npc(npc_id, npc_nome, npc_tipo, respeito):

    if npc_tipo == 'recepcionista':

        print("\n--- RECEPCIONISTA ---")
        print("Recepcionista: Olá estagiário, outros funcionários relataram um problema para você resolver.")

        pcs_com_problemas = listar_pcs_com_problemas()

        if pcs_com_problemas:
            print(f"Recepcionista: Os seguintes PCs estão com problemas: {', '.join(f'PC #{pc}' for pc in pcs_com_problemas)}")
        else:
            print("Recepcionista: No momento, todos os PCs estão funcionando normalmente!")

        print("Recepcionista: Boa sorte com os reparos!")
        print("--------------------")


    elif npc_tipo == 'colega':

        print("\n--- COLEGA ---")
        print("Colega: Oi! Como vai o trabalho?")
        print("\nOpções:")
        print("  [1] Perguntar sobre o trabalho")
        print("  [2] Pedir ajuda")
        print("  [3] Sair")

        escolha = input("\nSua escolha: ").strip()

        if escolha == '1':
            print("Colega: Está tudo corrido por aqui. Os computadores vivem dando problema!")
        elif escolha == '2':
            print("Colega: Infelizmente não posso ajudar muito, mas boa sorte!")
        elif escolha == '3':
            print("Colega: Até mais!")
        else:
            print("Colega: Não entendi...")


    elif npc_tipo == 'chefe':

        print("\n--- CHEFE ---")
        print("Chefe: Estagiário! Como anda o trabalho?")
        print("\nOpções:")
        print("  [1] Relatório de progresso")
        print("  [2] Pedir orientação")
        print("  [3] Sair")

        escolha = input("\nSua escolha: ").strip()

        if escolha == '1':
            print("Chefe: Ótimo! Continue assim. A empresa precisa de pessoas dedicadas.")
        elif escolha == '2':
            print("Chefe: Foque nos PCs com problemas. É prioridade máxima!")
        elif escolha == '3':
            print("Chefe: Volte ao trabalho!")
        else:
            print("Chefe: Concentre-se no que eu disse!")

        
    elif npc_tipo == 'almoxarife':
        
        print("\n--- ALMOXARIFE ---")
        print("Almoxarife: Precisa de algum equipamento?")
        print("\nOpções:")
        print("  [1] Ver equipamentos disponíveis")
        print("  [2] Pedir ferramenta")
        print("  [3] Sair")

        escolha = input("\nSua escolha: ").strip()

        if escolha == '1':
            print("Almoxarife: Temos cabos, teclados, mouses... o básico para manutenção.")
        elif escolha == '2':
            print("Almoxarife: Pegue o que precisar, mas devolva depois!")
        elif escolha == '3':
            print("Almoxarife: Até logo!")
        else:
            print("Almoxarife: Não temos isso aqui...")

    elif npc_tipo == 'supervisor':
        
        print("\n--- ALMOXARIFE ---")
        print("Almoxarife: Precisa de algum equipamento?")
        print("\nOpções:")
        print("  [1] Ver equipamentos disponíveis")
        print("  [2] Pedir ferramenta")
        print("  [3] Sair")

        escolha = input("\nSua escolha: ").strip()

        if escolha == '1':
            print("Almoxarife: Temos cabos, teclados, mouses... o básico para manutenção.")
        elif escolha == '2':
            print("Almoxarife: Pegue o que precisar, mas devolva depois!")
        elif escolha == '3':
            print("Almoxarife: Até logo!")
        else:
            print("Almoxarife: Não temos isso aqui...")

    input("\nPressione Enter para continuar...")
