# 2025-01-Estagio-de-TI
Jogo que ira retratar os passos de um aluno até conseguir um estágio de TI.

<div align="center"> <img src="docs/assets/imagens/download (4).png" height="auto" width="auto"/> </div>
<p style="text-align: center"><PERSON><PERSON> - Monica <PERSON></p>

Repositório para desenvolvimento de um Estagiário que tera a jornada da Impressora ao Dev Sênior da disciplina de SBD1 - 2025.1

## 🤝 Contribuidores

<div align="center">

| Nome                                                                      | Matrícula |
| :------------------------------------------------------------------------ | :-------: |
| [Caio Mesquita Vieira](https://github.com/Caiomesvie)                     | 222024283 |
| [Em<PERSON><PERSON> <PERSON>](https://github.com/EmivaltoJrr)        | 180100271 |
| [<PERSON>](https://github.com/<PERSON>)                   | 202023663 |
| [<PERSON>que](https://github.com/GustavoHenriqueRS)                  | 211030783 |
| [Lucas Mendonça Arruda](https://github.com/lucasarruda9)                  | 231035454 |

</div>

## 🎮 Jogo

um jovem estagiário de TI cheio de sonhos. Seu objetivo? Ser contratado como Desenvolvedor Júnior numa das maiores empresas de tecnologia da cidade.  
Mas para isso, você precisa provar seu valor — começando pelo básico (como instalar impressoras e arrumar cabos) até tarefas cada vez mais desafiadoras (programar sistemas, integrar APIs, criar bancos de dados).  
Cada *andar do prédio* representa uma etapa da sua evolução dentro da empresa!



### Estrutura do Jogo

- *Subsolo 2 (Servidor):* onde está alocado o servidor da empresa, cuidado com o frio!.
- *Subsolo 1 (Almoxarifado):* Itens de suporte (peças, cabos, drivers, ferramentas).
- *Térreo (Recepção):* Base para missões iniciais, conversar com NPCs e receber instruções.
- *Andares 1 a 10:* Cada andar é uma "área" ou "setor" da empresa com desafios diferentes.
- *Cafeteria (do outro lado da rua):* Compra de power-ups (café, salgados, energéticos que dão buffs).


### Estrutura dos andares

Neste RPG, você é um estagiário de TI em busca da tão sonhada efetivação. Cada andar de um prédio corporativo representa um novo desafio técnico e profissional. Supere todos os andares para alcançar o topo e conquistar seu cargo!

---

#### 🧱 1º Andar: Suporte Básico
**Desafios:**
- Trocar cabo de rede
- Reiniciar modem
- Instalar impressora
- Ajudar usuário a ligar o monitor

**Tema:** Help Desk, "reino das perguntas óbvias".

---

#### 🧰 2º Andar: Problemas de Hardware
**Desafios:**
- Trocar peças do PC
- Identificar defeito na placa-mãe
- Resolver máquina que não liga

**Tema:** "Necrotério dos PCs Antigos".

---

#### 💻 3º Andar: Suporte Remoto e Atendimento
**Desafios:**
- Atender chamados
- Lidar com usuários difíceis
- Acessar máquinas via TeamViewer
- Explicar termos técnicos de forma simples

**Tema:** "O Call Center dos Lamentos".

---

#### 🧵 4º Andar: Cabeamento e Infraestrutura
**Desafios:**
- Montar rack
- Organizar cabeamento
- Identificar patch panels
- Configurar switch

**Tema:** "Labirinto dos Cabos Infinitos".

---

#### 🖧 5º Andar: Servidores e Redes
**Desafios:**
- Configurar IP fixo
- Liberar portas
- Montar servidor local
- Resolver DHCP mal configurado

**Tema:** "Sala do Frio" (com ar-condicionado congelante).

---

#### 🔐 6º Andar: Segurança da Informação
**Desafios:**
- Identificar phishing
- Aplicar políticas de senha
- Montar firewall básico
- Remover vírus

**Tema:** "Bunker da Cibersegurança".

---

#### 🌐 7º Andar: Desenvolvimento Web
**Desafios:**
- Corrigir bugs em HTML/CSS/JS
- Fazer site responsivo
- Integrar API

**Tema:** "Reino do Stack Overflow".

---

#### ⚙️ 8º Andar: Backend e Integrações
**Desafios:**
- Lidar com banco de dados
- Criar API REST
- Resolver erros 500 misteriosos

**Tema:** "Masmorra do NullPointerException".

---

#### 🛠️ 9º Andar: Automatizações e DevOps
**Desafios:**
- Criar script de automação
- Configurar CI/CD
- Usar Docker
- Controlar versões com Git

**Tema:** "Fortaleza dos Scripts Eternos".

---

#### 👔 10º Andar: Boss Final - Diretoria + Incidente Crítico
**Desafios:**
- Apresentar projeto para a diretoria
- Lidar com pane geral na rede
- Restaurar backup
- Manter a calma sob pressão

**Tema:** "A Sala do Chefe Supremo".

**Recompensa:** 🎉 Efetivação! E talvez... um vale-refeição decente.

---

Boa sorte, estagiário! O futuro da TI está em suas mãos.

### Como rodar a documentação?

```bash
mkdocs serve
```


### Como jogar?

Primeiramente é necessário  clonar o repositório com o seguinte comando no terminal

```
# Para ter o repositório localmente
git clone https://github.com/SBD1/2025.01-Estagio-de-TI.git

# Diretório correto
cd 2025.01-Estagio-de-TI

```
Após entrar no diretório raíz, será necessário instalar as dependências do projeto
```
sudo apt install python3.10.12

# Criação do ambiente virtual
python3 -m venv env

# Ativação do ambiente virtual
source env/bin/activate

# Instalação das dependências
pip install -r requirements.txt


```

OBS: Caso o jogo não rode dentro do ambiente virtual, dê um "deactivate" no terminal, tente instalar as dependências fora do ambiente e rodar o jogo normalmente.

Após a instalação das dependências, será necessário instalar o docker Compose V1 e depois instalar o plugin Docker compose V2.

Com Tudo preparado basta apenas rodar os seguintes comandos no terminal
```
# subindo container do banco
docker compose up -d

# Iniciando jogo
python3 jogo.py

# liberando container (quando parar de jogar)
docker compose down

```
Instale Software necessários 
- [VsCode](https://code.visualstudio.com/download) ou IDE de preferência
- [PostgreSQL no Linux](https://www.hostinger.com.br/tutoriais/instalar-postgresql-ubuntu-20-04) ou [PostgreSQL no Windows](https://forum.casadodesenvolvedor.com.br/topic/46698-como-instalar-o-banco-de-dados-postgresql-no-windows/) (necessário sem o uso do docker)

## 📎 Apresentações

* [Apresentação módulo 1](https://youtu.be/txNLLvrHGxQ)

* [Apresentação módulo 2](https://youtu.be/Z8E0RvsN1to)

* [Apresentação módulo 3]()


</div>

## 📁 Entregas

- Módulo 1

  - [Diagrama Entidade-Relacionamento](./docs/modulo1/der.md)
  - [Dicionário de Dados](./docs/modulo1/dd.md)
  - [Modelo Relacional](./docs/modulo1/mr.md)

- Módulo 2

  - [Normalização](./docs/modulo2/norm.md)
  - [DDL](./docs/modulo2/ddl.md)
  - [DML](./docs/modulo2/dml.md)
  - [DQL](./docs/modulo2/dql.md)
  - [Álgebra Relacional](./docs/modulo2/algebra_relacional.md)

- Módulo 3

  - [Funcionamento do Jogo](./docs/modulo3/jogo.md)
  - [Triggers](./docs/modulo3/triggers.md)
  - [Stored Procedures](./docs/modulo3/procedimentos.md)


## Histórico de Versão

| Versão | Data | Descrição | Autor(es) | Revisor |
| :-: | :-: | :-: | :-: | :-: |
| `1.0`  | 02/05/2025 | Primeira versão  do MER  | [Emivalto da Costa Tavares Junior](https://github.com/EmivaltoJrr)  | [Gabriel Basto Bertolazi](https://github.com/Bertolazi) |
| `1.1`  | 08/06/2025 | Adicionando guia para executar o jogo  |  [Lucas Mendonça Arruda](https://github.com/lucasarruda9)  | [Caio Mesquita Vieira](https://github.com/Caiomesvie)  |
