# Estagiario de TI

<div align="center"> <img src="./assets/imagens/download (4).png" height="auto" width="auto"/> </div>
<div align="center">Repositório para desenvolvimento de um MUD que ira retratar os passo de um aluno até conseguir um estágio de TI, para a disciplina de SBD1 - 2025.1</div>

## Jogo

um jovem estagiário de TI cheio de sonhos. Seu objetivo? Ser contratado como Desenvolvedor Júnior em uma das maiores empresas de tecnologia do Brasil.  
Mas para isso, você precisa provar seu valor — começando pelo básico (as tarefas de estagiário que todos nós conhecemos).
Cada *andar do prédio* representa uma etapa da sua evolução dentro da empresa, e para avançá-los o nosso estagiário terá que passar pelos maiores inimigos de um estagiário dentro de uma empresa, como: O dev júnior que se acha sênior, o estagiário que tem certeza que é melhor que todo mundo, e muitos outros vilões para conseguir ser contratado.

### Como o jogo funciona?
O jogo "Estagiário: Da Impressora ao Dev Sênior" segue a jornada de um estagiário que precisa progredir em uma empresa, partindo doo andares mais baixos visando conseguir chegar à cobertura (RH e Diretoria).

- **Exploração e Combate**: No almoxarifado, o estagiário encontra itens úteis, como itens de ataque (periféricos para o seu computador), e de defesa (vestimentas). No térreo (recepção), ele recebe as missões iniciais e interage com personagens que o orientam. Nos andares de 1 a 9, cada um com um tema diferente, o estagiário deve completar as missões disponíveis, ganhando XP (QI) e moedas (dinheiro).

- **Power-ups e Armas Secundárias**:Fora da empresa, na cafeteria, o estagiário pode comprar power-ups, como café e energéticos, que lhe conferem efeitos de aumento de stamina. Finalmente, na cobertura, o estagiário enfrenta o desafio final: uma entrevista de emprego e a realização de um projeto completo, utilizando as habilidades desbloqueadas ao longo do jogo. Ao concluir essa etapa, o estagiário se torna um desenvolvedor sênior.

## Contribuidores


<center>
<table>
  <tr>
    <td align="center">
      <a href="https://github.com/Caiomesvie">
        <img src="https://github.com/Caiomesvie.png" width="190" style="border-radius: 50%;" alt="Caio Mesquita Vieira"/>
        <br/><sub><b>Caio Mesquita Vieira - 222024283</b></sub>
      </a>
    </td>
    <td align="center">
      <a href="https://github.com/EmivaltoJrr">
        <img src="https://github.com/EmivaltoJrr.png" width="190" style="border-radius: 50%;" alt="Márcio Henrique"/>
        <br/><sub><b>Emivalto da Costa Tavares Junior - 180100271</b></sub>
      </a>
    </td>
    <td align="center">
      <a href="https://github.com/Bertolazi">
        <img src="https://github.com/Bertolazi.png" width="190" style="border-radius: 50%;" alt="Gabriel Basto Bertolazi"/>
        <br/><sub><b>Gabriel Basto Bertolazi - 202023663</b></sub>
      </a>
    </td>
    <td align="center">
      <a href="https://github.com/GustavoHenriqueRS">
        <img src="https://github.com/GustavoHenriqueRS.png" width="190" style="border-radius: 50%;" alt="Gustavo Henrique"/>
        <br/><sub><b>Gustavo Henrique - 211030783</b></sub>
      </a>
    </td>
    <td align="center">
      <a href="https://github.com/lucasarruda9">
        <img src="https://github.com/lucasarruda9.png" width="190" style="border-radius: 50%;" alt="Márcio Henrique"/>
        <br/><sub><b>Lucas Mendonça Arruda - 231035454</b></sub>
      </a>
    </td>
  </tr>
</table>
</center>

## Histórico de Versão

| Versão | Data | Descrição | Autor(es) | Revisor |
| :-: | :-: | :-: | :-: | :-: |
| `1.0`  | 02/05/2025 | Primeira versão  do MER  | [Emivalto da Costa Tavares Junior](https://github.com/EmivaltoJrr)  | [Gabriel Basto Bertolazi](https://github.com/Bertolazi) |
| `1.1`  | 13/06/2025 | Primeira versão  do MER  | [Gabriel Basto Bertolazi](https://github.com/Bertolazi)  |  |