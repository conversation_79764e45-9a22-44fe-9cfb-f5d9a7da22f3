services:
  banco_de_dados:
    image: postgres:15
    container_name: banco_de_dados
    environment:
      POSTGRES_USER: jogador
      POSTGRES_PASSWORD: sbd1_password
      POSTGRES_DB: jogo
    ports:
      - "6000:5432"
    volumes:
      # Mapeia nosso script de inicialização único para a pasta correta no container
      - postgres_data:/var/lib/postgresql/data
  controla_migracoes:
    image: flyway/flyway:latest
    container_name: migracoes
    depends_on: 
      - banco_de_dados
    environment:
      FLYWAY_URL: ******************************************
      FLYWAY_USER: jogador
      FLYWAY_PASSWORD: sbd1_password  # Corrigido: sdb1 -> sbd1
    volumes:
      - ./src/migracoes:/flyway/sql
    entrypoint: ["flyway", "migrate"]

volumes:
  postgres_data: